## Setup local env

Postgres

```
docker run --name postgres \
  -e POSTGRES_PASSWORD=postgres \
  -v "$(pwd)/data:/var/lib/postgresql/data" \
  -p 5432:5432 \
  -d postgres
```

## Secrets
Navigate to OdmoriBa.Api project
Execute:
dotnet user-secrets set "Firebase:AdminServiceAccount" 'FIREBASE-SERVICE-ACCOUNT-JSON'

Navigate to OdmoriBa.Web project
Execute:
dotnet user-secrets set "Firebase:AdminServiceAccount" 'GOOGLE_MAPS_KEY'

dotnet user-secrets set "Google:MapsKey" 'FIREBASE-SERVICE-ACCOUNT-JSON'

### 2. That's all! Run OdmoriBa.Api project

## Database Migrations

1. Navigate to src folder
2. Create migration:
   ``` dotnet ef migrations add Initial9 -p Infrastructure/OdmoriBa.Infrastructure -s Presentation/OdmoriBa.Api -o Data/Migrations ```
3. Update database: ``` dotnet ef database update -p Infrastructure/OdmoriBa.Infrastructure -s Presentation/OdmoriBa.Api ```


## Deploy on DEV

```
docker build -f Dockerfile-web -t odmori-web .
docker build -f Dockerfile-api -t odmori-api .
```

Docker compose
```
docker-compose -f docker-compose.yaml -p odmori up -d
```
