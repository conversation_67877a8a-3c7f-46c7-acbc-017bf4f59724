// Google Maps integration for OdmoriBa
window.GoogleMapsHelper = {
    map: null,
    marker: null,
    dotNetRef: null,
    mapClickListener: null,
    markerDragListener: null,
    autocomplete: null,
    searchInput: null,

    // Initialize the map
    initializeMap: function (elementId, dotNetReference, apiKey, lat = 44.2619, lng = 17.2678, searchInputId = null) {
        this.dotNetRef = dotNetReference;
        
        // Default to Bosnia and Herzegovina center if no coordinates provided
        const center = { lat: lat, lng: lng };
        
        // Create map
        this.map = new google.maps.Map(document.getElementById(elementId), {
            zoom: lat === 44.2619 && lng === 17.2678 ? 8 : 15, // Zoom out if default location, zoom in if specific
            center: center,
            mapTypeControl: true,
            streetViewControl: true,
            fullscreenControl: true,
            zoomControl: true,
        });

        // Create marker
        this.marker = new google.maps.Marker({
            position: center,
            map: this.map,
            draggable: true,
            title: 'Drag to set location'
        });

        // Add click listener to map using modern approach
        this.mapClickListener = google.maps.event.addListener(this.map, 'click', (event) => {
            this.updateMarkerPosition(event.latLng);
        });

        // Add drag listener to marker using modern approach
        this.markerDragListener = google.maps.event.addListener(this.marker, 'dragend', (event) => {
            this.updateMarkerPosition(event.latLng);
        });

        // If specific coordinates were provided, don't show marker initially for default location
        if (lat === 44.2619 && lng === 17.2678) {
            this.marker.setVisible(false);
        }

        // Initialize search functionality if search input is provided
        if (searchInputId) {
            this.initializeSearch(searchInputId);
        }
    },

    // Initialize search functionality
    initializeSearch: function (searchInputId) {
        this.searchInput = document.getElementById(searchInputId);
        if (!this.searchInput) {
            // Notify Blazor component about the error
            if (this.dotNetRef) {
                this.dotNetRef.invokeMethodAsync('OnSearchInitializationError', 'Search input element not found: ' + searchInputId);
            }
            return;
        }

        // Create autocomplete instance
        this.autocomplete = new google.maps.places.Autocomplete(this.searchInput, {
            types: ['establishment', 'geocode', 'address'],
            componentRestrictions: { country: ['ba', 'hr', 'rs', 'me', 'si'] }, // Balkan countries
            fields: ['place_id', 'geometry', 'name', 'formatted_address', 'types']
        });

        // Bind autocomplete to map bounds
        this.autocomplete.bindTo('bounds', this.map);

        // Listen for place selection
        google.maps.event.addListener(this.autocomplete, 'place_changed', () => {
            const place = this.autocomplete.getPlace();

            // Check if place has geometry
            if (!place.geometry || !place.geometry.location) {
                // Try to geocode the place name if no geometry is available
                if (place.name || place.formatted_address) {
                    this.geocodePlace(place.name || place.formatted_address);
                } else {
                    // Notify Blazor component about the error
                    if (this.dotNetRef) {
                        this.dotNetRef.invokeMethodAsync('OnSearchError', 'No location data found for: ' + (place.name || place.formatted_address || 'Unknown place'));
                    }
                }
                return;
            }

            // Update map and marker position
            this.map.setCenter(place.geometry.location);
            this.map.setZoom(17); // Zoom in closer for search results
            this.updateMarkerPosition(place.geometry.location);

            // Clear the search input
            this.searchInput.value = '';
        });
    },

    // Geocode place name as fallback when autocomplete doesn't provide geometry
    geocodePlace: function (placeName) {
        const geocoder = new google.maps.Geocoder();

        geocoder.geocode({
            address: placeName,
            componentRestrictions: { country: ['BA', 'HR', 'RS', 'ME', 'SI'] }
        }, (results, status) => {
            if (status === 'OK' && results[0]) {
                const location = results[0].geometry.location;

                // Update map and marker position
                this.map.setCenter(location);
                this.map.setZoom(15); // Slightly less zoom for geocoded results
                this.updateMarkerPosition(location);

                // Clear the search input
                this.searchInput.value = '';
            } else {
                // Notify Blazor component about the geocoding failure
                if (this.dotNetRef) {
                    this.dotNetRef.invokeMethodAsync('OnSearchError', 'Could not find location for: ' + placeName);
                }
            }
        });
    },

    // Update marker position and notify Blazor component
    updateMarkerPosition: function (latLng) {
        this.marker.setPosition(latLng);
        this.marker.setVisible(true);
        
        const lat = latLng.lat();
        const lng = latLng.lng();
        
        // Notify Blazor component
        if (this.dotNetRef) {
            this.dotNetRef.invokeMethodAsync('OnLocationChanged', lat, lng);
        }
    },

    // Set marker position from external source (e.g., input fields)
    setMarkerPosition: function (lat, lng) {
        if (this.map && this.marker) {
            const position = new google.maps.LatLng(lat, lng);
            this.marker.setPosition(position);
            this.marker.setVisible(true);
            this.map.setCenter(position);
            this.map.setZoom(15);
        }
    },

    // Clear marker
    clearMarker: function () {
        if (this.marker) {
            this.marker.setVisible(false);
        }
    },

    // Dispose resources
    dispose: function () {
        // Remove event listeners to prevent memory leaks
        if (this.mapClickListener) {
            google.maps.event.removeListener(this.mapClickListener);
            this.mapClickListener = null;
        }
        if (this.markerDragListener) {
            google.maps.event.removeListener(this.markerDragListener);
            this.markerDragListener = null;
        }

        // Clean up autocomplete
        if (this.autocomplete) {
            google.maps.event.clearInstanceListeners(this.autocomplete);
            this.autocomplete = null;
        }

        this.map = null;
        this.marker = null;
        this.dotNetRef = null;
        this.searchInput = null;
    }
};

// Load Google Maps API dynamically
window.loadGoogleMapsApi = function (apiKey) {
    return new Promise((resolve, reject) => {
        // Check if already loaded
        if (window.google && window.google.maps) {
            resolve();
            return;
        }

        // Create script element
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        script.async = true;
        script.defer = true;
        
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('Failed to load Google Maps API'));
        
        document.head.appendChild(script);
    });
};
