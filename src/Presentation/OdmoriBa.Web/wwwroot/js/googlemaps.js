// Google Maps integration for OdmoriBa
window.GoogleMapsHelper = {
    map: null,
    marker: null,
    dotNetRef: null,

    // Initialize the map
    initializeMap: function (elementId, dotNetReference, apiKey, lat = 44.2619, lng = 17.2678) {
        this.dotNetRef = dotNetReference;
        
        // Default to Bosnia and Herzegovina center if no coordinates provided
        const center = { lat: lat, lng: lng };
        
        // Create map
        this.map = new google.maps.Map(document.getElementById(elementId), {
            zoom: lat === 44.2619 && lng === 17.2678 ? 8 : 15, // Zoom out if default location, zoom in if specific
            center: center,
            mapTypeControl: true,
            streetViewControl: true,
            fullscreenControl: true,
            zoomControl: true,
        });

        // Create marker
        this.marker = new google.maps.Marker({
            position: center,
            map: this.map,
            draggable: true,
            title: 'Drag to set location'
        });

        // Add click listener to map
        this.map.addListener('click', (event) => {
            this.updateMarkerPosition(event.latLng);
        });

        // Add drag listener to marker
        this.marker.addListener('dragend', (event) => {
            this.updateMarkerPosition(event.latLng);
        });

        // If specific coordinates were provided, don't show marker initially for default location
        if (lat === 44.2619 && lng === 17.2678) {
            this.marker.setVisible(false);
        }
    },

    // Update marker position and notify Blazor component
    updateMarkerPosition: function (latLng) {
        this.marker.setPosition(latLng);
        this.marker.setVisible(true);
        
        const lat = latLng.lat();
        const lng = latLng.lng();
        
        // Notify Blazor component
        if (this.dotNetRef) {
            this.dotNetRef.invokeMethodAsync('OnLocationChanged', lat, lng);
        }
    },

    // Set marker position from external source (e.g., input fields)
    setMarkerPosition: function (lat, lng) {
        if (this.map && this.marker) {
            const position = new google.maps.LatLng(lat, lng);
            this.marker.setPosition(position);
            this.marker.setVisible(true);
            this.map.setCenter(position);
            this.map.setZoom(15);
        }
    },

    // Clear marker
    clearMarker: function () {
        if (this.marker) {
            this.marker.setVisible(false);
        }
    },

    // Dispose resources
    dispose: function () {
        this.map = null;
        this.marker = null;
        this.dotNetRef = null;
    }
};

// Load Google Maps API dynamically
window.loadGoogleMapsApi = function (apiKey) {
    return new Promise((resolve, reject) => {
        // Check if already loaded
        if (window.google && window.google.maps) {
            resolve();
            return;
        }

        // Create script element
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        script.async = true;
        script.defer = true;
        
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('Failed to load Google Maps API'));
        
        document.head.appendChild(script);
    });
};
